# TOEFL iBT Listening Test - JSON-Driven Version

This is a flexible, JSON-driven version of the TOEFL iBT Listening test application. All questions, answers, audio files, and test settings are now loaded from JSON configuration files, making it easy to create different test versions without modifying code.

## Features

- **JSON-Driven Configuration**: All test data is stored in JSON files
- **Multiple Test Configurations**: Easy to switch between different test versions
- **Flexible Question Types**: Support for single-choice and multiple-choice questions
- **Audio Integration**: Real MP3 audio playback with controls
- **Comprehensive Reporting**: Detailed performance analysis and scoring
- **Responsive Design**: Works on desktop and mobile devices
- **Print-Friendly Reports**: Generate printable test reports

## File Structure

```
Listening/
├── index.html                 # Main HTML file
├── js/
│   └── app.js                # Main JavaScript application
├── config/
│   ├── test-config.json      # Full TOEFL test configuration
│   └── practice-test-1.json  # Practice test configuration
├── audio/
│   ├── C1.mp3               # Conversation 1 audio
│   ├── C2.mp3               # Conversation 2 audio
│   ├── L1.mp3               # Lecture 1 audio
│   ├── L2.mp3               # Lecture 2 audio
│   └── L3.mp3               # Lecture 3 audio
├── assets/
│   └── icon.png             # Test logo
└── README.md                # This file
```

## JSON Configuration Format

### Test Configuration Structure

```json
{
  "testInfo": {
    "title": "Test Title",
    "duration": 2160,           // Duration in seconds
    "logo": "assets/icon.png",
    "instructions": [           // Array of instruction strings
      "Main instruction...",
      "Bullet point 1...",
      "Bullet point 2..."
    ]
  },
  "passages": [
    {
      "id": "C1",
      "title": "Conversation 1: Topic",
      "type": "Conversation",    // or "Lecture"
      "audioFile": "audio/C1.mp3",
      "instruction": "Listen carefully...",
      "questions": [
        {
          "id": "C1Q1",
          "text": "Question text?",
          "type": "single",      // or "multiple"
          "options": [
            "Option A",
            "Option B",
            "Option C",
            "Option D"
          ],
          "correctAnswer": ["Option B"],  // Array for multiple correct answers
          "explanation": "Optional explanation"
        }
      ]
    }
  ],
  "scoring": {
    "passingScore": 70,
    "performanceLevels": {
      "excellent": {
        "min": 80,
        "message": "Performance message..."
      }
      // ... more levels
    }
  }
}
```

### Question Types

1. **Single Choice** (`"type": "single"`):
   - Only one correct answer
   - `correctAnswer` should contain one item

2. **Multiple Choice** (`"type": "multiple"`):
   - Multiple correct answers possible
   - `correctAnswer` should contain all correct options

## How to Create New Tests

1. **Copy an existing configuration file** from the `config/` folder
2. **Modify the test information**:
   - Change title, duration, instructions
   - Update logo path if needed

3. **Update passages**:
   - Change passage titles and types
   - Update audio file paths
   - Modify questions, options, and correct answers

4. **Adjust scoring criteria**:
   - Set passing score
   - Customize performance level messages

5. **Add audio files** to the `audio/` folder

6. **Update the HTML** (optional):
   - Add new configuration option to the dropdown in `index.html`

## Usage Instructions

1. **Open `index.html`** in a web browser
2. **Select a test configuration** from the dropdown
3. **Click "Load Selected Test"** to load the configuration
4. **Review instructions** and click "Start Test"
5. **Listen to audio** and answer questions
6. **View detailed results** at the end

## Audio Requirements

- Audio files should be in MP3 format
- Place all audio files in the `audio/` folder
- Ensure file names match those specified in the JSON configuration
- Audio files should be properly encoded for web playback

## Customization Options

### Adding New Test Configurations

1. Create a new JSON file in the `config/` folder
2. Follow the configuration format described above
3. Add the new option to the dropdown in `index.html`

### Modifying Scoring

Edit the `scoring` section in your JSON configuration:
- Adjust `passingScore`
- Modify performance level thresholds and messages
- Add or remove performance levels

### Changing UI Elements

- **Logo**: Update the `logo` path in `testInfo`
- **Colors**: Modify CSS classes in `index.html`
- **Instructions**: Edit the `instructions` array in `testInfo`

## Browser Compatibility

- Modern browsers with ES6+ support
- Chrome, Firefox, Safari, Edge (recent versions)
- Mobile browsers supported

## Troubleshooting

### Audio Not Playing
- Check that MP3 files exist in the `audio/` folder
- Verify file names match the JSON configuration
- Ensure browser supports MP3 playback

### Configuration Not Loading
- Check JSON syntax using a JSON validator
- Verify file paths are correct
- Check browser console for error messages

### Questions Not Displaying
- Ensure `questions` array is properly formatted
- Check that `options` and `correctAnswer` arrays are valid
- Verify question `type` is either "single" or "multiple"

## Technical Notes

- The application uses vanilla JavaScript (no frameworks required)
- Tailwind CSS is loaded from CDN for styling
- Audio playback uses the HTML5 `<audio>` element
- Configuration files are loaded using the Fetch API

## Future Enhancements

Potential improvements that could be added:
- Question randomization
- Time limits per question
- Audio transcripts
- Difficulty levels
- Progress saving/resuming
- Multiple language support
- Question categories and tagging
