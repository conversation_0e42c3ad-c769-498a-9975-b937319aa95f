<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TOEFL iBT Listening Mock Exam</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .question-option {
            transition: all 0.2s ease-in-out;
        }
        .question-option.selected {
            background-color: #3b82f6; /* bg-blue-600 */
            color: white;
            border-color: #3b82f6; /* border-blue-600 */
        }
        .question-option:hover:not(.selected) {
            background-color: #eff6ff; /* bg-blue-50 */
        }
        /* Custom scrollbar for better aesthetics */
        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #f1f5f9; /* slate-100 */
        }
        ::-webkit-scrollbar-thumb {
            background: #94a3b8; /* slate-400 */
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #64748b; /* slate-500 */
        }

        /* Print styles */
        @media print {
            body {
                background: white !important;
            }
            #app-container {
                box-shadow: none !important;
                max-width: none !important;
            }
            header {
                background: #1e293b !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            .bg-blue-50, .bg-green-50, .bg-red-50, .bg-yellow-50, .bg-purple-50, .bg-slate-50 {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
            button {
                display: none !important;
            }
            .shadow-md, .shadow-2xl {
                box-shadow: none !important;
                border: 1px solid #e2e8f0 !important;
            }
        }
    </style>
</head>
<body class="bg-slate-100 flex items-center justify-center min-h-screen p-4">
    <!-- Audio element to play real MP3s -->
    <audio id="audio-element" class="hidden"></audio>

    <div id="app-container" class="w-full max-w-5xl mx-auto bg-white rounded-lg shadow-2xl overflow-hidden">
        
        <!-- Header -->
        <header class="bg-slate-800 text-white p-4 flex justify-between items-center">
            <div class="flex items-center space-x-3">
                <!-- Logo -->
                <div class="w-10 h-10 flex-shrink-0">
                    <img src="icon.png" alt="TOEFL Logo" class="w-10 h-10 object-contain rounded-md">
                </div>
                <h1 class="text-xl font-bold">TOEFL iBT Listening Section</h1>
            </div>
            <div id="timer" class="text-lg font-mono bg-slate-700 px-4 py-1 rounded-md">36:00</div>
        </header>

        <main id="main-content" class="p-6 md:p-8">
            <!-- Start Screen -->
            <div id="start-screen">
                <h2 class="text-2xl font-bold text-slate-800 mb-4">Instructions</h2>
                <p class="text-slate-600 mb-6">This is a simulation of the TOEFL iBT Listening section. You will have 36 minutes to complete 5 passages and 28 questions.</p>
                <ul class="list-disc list-inside text-slate-600 mb-8 space-y-2">
                    <li>The test will proceed automatically from one passage to the next.</li>
                    <li>You must answer each question before moving to the next. You cannot go back.</li>
                    <li>Take notes while the audio is playing.</li>
                    <li>The timer will start as soon as you begin the test.</li>
                    <li class="font-semibold text-amber-700">Important: Place your MP3 files (C1.mp3, L1.mp3, etc.) in the same folder as this HTML file for the audio to work.</li>
                </ul>
                <button id="start-btn" class="w-full bg-blue-600 text-white font-bold py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">Start Test</button>
            </div>

            <!-- Audio Player Screen -->
            <div id="audio-player-screen" class="hidden text-center">
                 <h2 id="passage-title" class="text-2xl font-bold text-slate-800 mb-4"></h2>
                 <p id="passage-instruction" class="text-slate-600 mb-6"></p>

                 <!-- Audio Controls -->
                 <div class="bg-slate-50 rounded-lg p-6 mb-6 max-w-2xl mx-auto">
                    <div class="flex items-center justify-center space-x-4 mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                          <path stroke-linecap="round" stroke-linejoin="round" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M5.636 8.464a5 5 0 000 7.072m2.828 9.9a9 9 0 000-12.728" />
                        </svg>
                        <p class="text-lg font-semibold text-slate-700">Audio is playing... Please listen and take notes.</p>
                    </div>

                    <!-- Time Display -->
                    <div class="flex justify-between text-sm text-slate-600 mb-2">
                        <span id="current-time">0:00</span>
                        <span id="total-duration">0:00</span>
                    </div>

                    <!-- Progress Bar (Draggable) -->
                    <div class="relative w-full bg-slate-200 rounded-full h-3 mb-4 cursor-pointer" id="progress-container">
                        <div id="progress-bar" class="bg-blue-600 h-3 rounded-full relative" style="width: 0%">
                            <div class="absolute right-0 top-1/2 transform -translate-y-1/2 w-4 h-4 bg-blue-600 rounded-full border-2 border-white shadow-md cursor-grab active:cursor-grabbing" id="progress-handle"></div>
                        </div>
                    </div>

                    <!-- Control Buttons -->
                    <div class="flex justify-center space-x-4">
                        <button id="play-pause-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <svg id="play-icon" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V6a2 2 0 012-2z" />
                            </svg>
                            <span id="play-pause-text">Pause</span>
                        </button>
                        <button id="skip-back-btn" class="bg-slate-600 hover:bg-slate-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0019 16V8a1 1 0 00-1.6-.8l-5.334 4zM4.066 11.2a1 1 0 000 1.6l5.334 4A1 1 0 0011 16V8a1 1 0 00-1.6-.8l-5.334 4z" />
                            </svg>
                            <span>-10s</span>
                        </button>
                        <button id="skip-forward-btn" class="bg-slate-600 hover:bg-slate-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.933 12.8a1 1 0 000-1.6L6.6 7.2A1 1 0 005 8v8a1 1 0 001.6.8l5.333-4zM19.933 12.8a1 1 0 000-1.6l-5.333-4A1 1 0 0013 8v8a1 1 0 001.6.8l5.333-4z" />
                            </svg>
                            <span>+10s</span>
                        </button>
                        <button id="skip-audio-btn" class="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>Skip Audio</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Question Screen -->
            <div id="question-screen" class="hidden">
                <div class="flex justify-between items-center mb-4">
                    <h3 id="question-passage-title" class="text-lg font-semibold text-slate-700"></h3>
                    <div id="question-counter" class="text-sm font-medium text-slate-500"></div>
                </div>
                <div id="question-text" class="text-lg text-slate-800 mb-6 font-medium"></div>
                <div id="question-options" class="space-y-3"></div>
                <div class="mt-8 flex justify-end">
                    <button id="next-btn" class="bg-blue-600 text-white font-bold py-2 px-8 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-slate-400" disabled>Next</button>
                </div>
            </div>

            <!-- Results Screen -->
            <div id="results-screen" class="hidden">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-slate-800 mb-4">TOEFL iBT Listening Test Report</h2>
                    <div class="bg-blue-50 border-2 border-blue-200 rounded-lg p-6 inline-block">
                        <p class="text-lg text-slate-700">Overall Score</p>
                        <p class="text-5xl font-bold text-blue-600 my-2"><span id="score"></span> / <span id="total-questions"></span></p>
                        <p class="text-lg text-slate-700">(<span id="percentage-score"></span>%)</p>
                    </div>
                </div>

                <!-- Performance Summary -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-xl font-bold text-slate-800 mb-4">Performance Summary</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="passage-summary">
                        <!-- Passage summaries will be inserted here -->
                    </div>
                </div>

                <!-- Detailed Question Analysis -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-xl font-bold text-slate-800 mb-4">Detailed Question Analysis</h3>
                    <div id="question-analysis">
                        <!-- Question analysis will be inserted here -->
                    </div>
                </div>

                <!-- Overall Analysis -->
                <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                    <h3 class="text-xl font-bold text-slate-800 mb-4">Overall Analysis</h3>
                    <div id="overall-analysis" class="text-slate-700">
                        <!-- Overall analysis will be inserted here -->
                    </div>
                </div>

                <div class="text-center">
                    <button id="restart-btn" class="bg-slate-600 text-white font-bold py-3 px-8 rounded-lg hover:bg-slate-700 transition-colors mr-4">Take Again</button>
                    <button id="print-report-btn" class="bg-blue-600 text-white font-bold py-3 px-8 rounded-lg hover:bg-blue-700 transition-colors">Print Report</button>
                </div>
            </div>

        </main>
    </div>

    <script>
        // --- DATA STRUCTURE ---
        // Manually extracted from the provided PDF.
        // NOTE: The official test has 5 passages (2 Conversations, 3 Lectures).
        // I am using C1, C2, L1, L2, L3 from your materials to match the official structure.
        const passages = [
            {
                id: 'C1',
                title: 'Conversation 1: Mimicry',
                type: 'Conversation',
                audioFile: 'C1.mp3',
                questions: [
                    { q: "Why does the student go to see the professor?", options: ["He wants feedback on his topic proposal for a presentation.", "He needs to better understand the benefits of a type of mimicry.", "He would like to find out more about what he missed from the previous class.", "He is unsure why mimicry is less common among insects than among other animals."], answer: "He needs to better understand the benefits of a type of mimicry." },
                    { q: "Why does the man mention bees and moths?", options: ["To show that he understands what Batesian mimicry is", "To cite an example given in class that he did not understand", "To identify two species that do not have any predators in common", "To give an example of two species that each adapted to look like a third species"], answer: "To show that he understands what Batesian mimicry is" },
                    { q: "What does the professor explain about Batesian mimicry?", options: ["Situations in which it is likely to occur", "Reasons why it is often exhibited by insects", "Why it is a more effective defense against predators than Mullerian mimicry is", "A condition that needs to be met for it to be an effective defense against predation"], answer: "A condition that needs to be met for it to be an effective defense against predation" },
                    { q: "What points does the professor make about the red postman and the common postman butterflies?", options: ["They can both be harmful to animals that prey on them.", "They are evolving into a single species.", "Their populations increase and decrease at the same rate.", "They each benefit from their physical similarities to the other."], answer: ["They can both be harmful to animals that prey on them.", "They each benefit from their physical similarities to the other."], type: 'multiple' },
                    { q: "What can be inferred about the professor at the end of the conversation?", options: ["She is worried that the student may unintentionally bring poisonous frogs to class.", "She is pleased that the class may have an opportunity to examine live animals.", "She thinks that bringing frogs to class would be more distracting than educational.", "She is not convinced that the frogs the student wants to bring to class are good examples of mimicry."], answer: "She thinks that bringing frogs to class would be more distracting than educational." }
                ]
            },
            {
                id: 'L1',
                title: 'Lecture 1: Utopian Literature',
                type: 'Lecture',
                audioFile: 'L1.mp3',
                questions: [
                    { q: "What does the professor mainly discuss?", options: ["Characteristics of writers who have produced utopian literature", "An example of an early type of utopian literature", "The influence of ancient Greek utopian authors on travel writing", "Why some utopian literature evolved into dystopian literature"], answer: "An example of an early type of utopian literature" },
                    { q: "According to the speakers, what are two reasons why an author might write utopian literature?", options: ["To encourage exploration of unknown regions", "To point out faults in an actual society", "To criticize the idea of trying to create an ideal society", "To emphasize the wide variety of human societies"], answer: ["To point out faults in an actual society", "To criticize the idea of trying to create an ideal society"], type: 'multiple' },
                    { q: "Why does the professor mention a future society in which robots have taken over?", options: ["To suggest a similarity between ancient and modern societies", "To suggest that technology cannot change human nature", "To help illustrate the purpose of dystopian literature", "To point out that unbelievable plots are a feature of utopian literature"], answer: "To help illustrate the purpose of dystopian literature" },
                    { q: "What does the professor imply about the geographers who searched for the islands that Iambulus wrote about?", options: ["They were misled by Iambulus' inclusion of very specific details.", "They found some of the islands mentioned by Iambulus.", "They contributed significantly to the science of mapmaking.", "Their work was complicated by some inaccurate translations."], answer: "They were misled by Iambulus' inclusion of very specific details." },
                    { q: "What does the professor indicate about contemporary science fiction stories?", options: ["They tend to be more believable than dystopian stories.", "Their main characters often must solve a mystery.", "The best ones take place on Earth.", "Their plots are similar to those of extraordinary-voyage stories."], answer: "Their plots are similar to those of extraordinary-voyage stories." },
                    { q: "Why does the professor say this: 'Well, how much did the ancient Greeks know about the world beyond their immediate area?'", options: ["To determine how much the students know about the geography of Greece", "To acknowledge that there are still unexplored areas on Earth", "To imply that extraordinary-voyage stories were written by geographers", "To explain why extraordinary-voyage stories were thought to be true"], answer: "To explain why extraordinary-voyage stories were thought to be true" }
                ]
            },
            {
                id: 'C2',
                title: 'Conversation 2: Radio Station Partnership',
                type: 'Conversation',
                audioFile: 'C2.mp3',
                questions: [
                    { q: "What is the conversation mainly about?", options: ["The unexpected success of the student's recent news story", "Ideas to broaden the contents of the university's weekly news hour", "An employment opportunity for the student at a larger radio station", "The expected results of a new partnership between radio stations"], answer: "The expected results of a new partnership between radio stations" },
                    { q: "What can be inferred about the student's story about campus food?", options: ["She has revised it from an earlier version.", "She has adapted it from a feature story in a newspaper.", "She had difficulty getting information for the story.", "She plans to interview more people before she finishes the story."], answer: "She has revised it from an earlier version." },
                    { q: "What does the professor say about employees of the city's local radio station?", options: ["Many of the employees fill more than one role at the station.", "Many of the employees used to work at the university's radio station.", "Some of the employees will be invited to give lectures at the university.", "Students from the university's radio station will watch the employees do their jobs."], answer: ["Some of the employees will be invited to give lectures at the university.", "Students from the university's radio station will watch the employees do their jobs."], type: 'multiple' },
                    { q: "What points do the speakers make about the building that is being constructed?", options: ["It is part of an expanded business degree program.", "It will be the site of noncredit courses that are open to the community.", "It is located on a street in the city's business district.", "It is one location where guest lectures might be held.", "It involves a large construction site that is easily visible to the public."], answer: ["It is part of an expanded business degree program.", "It will be the site of noncredit courses that are open to the community.", "It involves a large construction site that is easily visible to the public."], type: 'multiple' },
                    { q: "What is the student's first reaction to the idea of her story being broadcast on the city's local radio station?", options: ["She feels nervous about working on the story with professional broadcasters.", "She worries that the story will lead to public objections to the new construction.", "She doubts that people outside the university would be interested in the story.", "She doubts that she can find enough time to update the story before it is broadcast."], answer: "She doubts that people outside the university would be interested in the story." }
                ]
            },
            {
                id: 'L2',
                title: 'Lecture 2: Visceral Fit',
                type: 'Lecture',
                audioFile: 'L2.mp3',
                questions: [
                    { q: "What is the lecture mainly about?", options: ["Methods of manipulating people's visceral states", "Research on how people make judgments about the future", "Ways to convince people that global warming is a reality", "Results of a study that provides new evidence of global warming"], answer: "Research on how people make judgments about the future" },
                    { q: "On what assumption did the researchers base their hypothesis?", options: ["It is easy to imagine a future physical state if one is currently experiencing that state.", "A person's visceral state can change frequently.", "Most university students believe that global warming is a real phenomenon.", "Weather trends influence people's beliefs about the future."], answer: "It is easy to imagine a future physical state if one is currently experiencing that state." },
                    { q: "Why does the student mention being hungry?", options: ["To compare hunger to other visceral states", "To suggest a subject for future study", "To offer an alternate hypothesis to the one suggested by the professor", "To check his understanding of a point raised by the professor"], answer: "To check his understanding of a point raised by the professor" },
                    { q: "Why did the researchers repeat their study indoors?", options: ["To attempt to avoid having participants use outdoor weather conditions to make judgements", "To allow participants to focus without many distractions", "To allow participants to experience a wider range of temperatures", "To determine whether direct sunlight had an effect on participants' judgements"], answer: "To attempt to avoid having participants use outdoor weather conditions to make judgements" },
                    { q: "What did the data from the experiment show?", options: ["Indoor temperature has no effect on people's judgments about global warming.", "The concept of visceral fit does not apply to beliefs about global warming.", "The experience of heat influences people's beliefs about global warming.", "People are more likely to express a belief in global warming when indoor and outdoor temperatures match."], answer: "The experience of heat influences people's beliefs about global warming." },
                    { q: "What does the student imply when he says this: 'But wouldn't most people already have an opinion on global warming? I mean, there's tons of information out there.'", options: ["Global warming has been conclusively proven by scientific research.", "The amount of information about global warming can be overwhelming.", "A university campus is not a suitable place for this type of study.", "Participants' previously held opinions would be the most influential factor in their responses."], answer: "Participants' previously held opinions would be the most influential factor in their responses." }
                ]
            },
            {
                id: 'L3',
                title: 'Lecture 3: The Pantheon',
                type: 'Lecture',
                audioFile: 'L3.mp3',
                questions: [
                    { q: "What is the main purpose of the lecture?", options: ["To explain the role of architecture in the culture of ancient Rome", "To show how an analysis of a Roman building led to a new understanding of Roman construction materials", "To introduce the uses of various construction materials made with volcanic ash", "To illustrate the impact that high-strength concrete had on ancient Roman architecture"], answer: "To show how an analysis of a Roman building led to a new understanding of Roman construction materials" },
                    { q: "Why does the professor discuss the results of an inspection of the Pantheon by city authorities?", options: ["To suggest that city authorities should conduct regular maintenance checks on ancient buildings", "To explain how the interior dimensions of the Pantheon were measured", "To explain why engineers studying the Pantheon changed their initial assumptions about its dome", "To show how the Romans disguised the fact that that Pantheon had been damaged"], answer: "To explain why engineers studying the Pantheon changed their initial assumptions about its dome" },
                    { q: "According to a computer model, what effect do the seven rings around the Pantheon's dome have on the structure?", options: ["They cause the dome to function as an inverted bowl.", "They strengthen parts of the structure that are under stress.", "They serve as an exterior decoration.", "They protect the roof from falling plaster."], answer: "They strengthen parts of the structure that are under stress." },
                    { q: "What opinion does the professor express about the Roman architectural revolution?", options: ["The most important factor was a tradition of experimenting with concrete construction.", "The Romans' progress in architecture should not be considered revolutionary.", "The actual period of innovation was shorter than some historians think.", "The idea that there was a revolution was originally based on a misunderstanding about the properties of Roman concrete."], answer: ["The most important factor was a tradition of experimenting with concrete construction.", "The actual period of innovation was shorter than some historians think."], type: 'multiple' },
                    { q: "What are three characteristics of concrete made with pozzolana cement?", options: ["It lacks tensile strength.", "It is heat resistant.", "It can be shaped into curves.", "It can last for hundreds of years.", "It can be carved."], answer: ["It lacks tensile strength.", "It can be shaped into curves.", "It can last for hundreds of years."], type: 'multiple' },
                    { q: "What does the professor imply when he says this: 'Modern day architects look at the dome and think, no way we could build that thing today without reinforcing that concrete with steel.'", options: ["The architects do not understand how the walls alone could support the weight of the dome.", "The architects believe that structures surviving from antiquity should be fitted with steel reinforcements.", "Modern safety regulations prevent architects from using pozzolana concrete.", "Modern-day techniques of concrete construction are superior to ancient Roman techniques."], answer: "The architects do not understand how the walls alone could support the weight of the dome." }
                ]
            }
        ];

        // --- APPLICATION STATE ---
        let currentPassageIndex = 0;
        let currentQuestionIndex = 0;
        let userAnswers = [];
        let timerInterval;
        let totalSeconds = 36 * 60;

        // --- DOM ELEMENTS ---
        const startScreen = document.getElementById('start-screen');
        const audioPlayerScreen = document.getElementById('audio-player-screen');
        const questionScreen = document.getElementById('question-screen');
        const resultsScreen = document.getElementById('results-screen');
        
        const startBtn = document.getElementById('start-btn');
        const nextBtn = document.getElementById('next-btn');
        const restartBtn = document.getElementById('restart-btn');

        const timerDisplay = document.getElementById('timer');
        const passageTitle = document.getElementById('passage-title');
        const passageInstruction = document.getElementById('passage-instruction');
        const progressBar = document.getElementById('progress-bar');
        const progressContainer = document.getElementById('progress-container');
        const progressHandle = document.getElementById('progress-handle');
        const currentTimeDisplay = document.getElementById('current-time');
        const totalDurationDisplay = document.getElementById('total-duration');

        const playPauseBtn = document.getElementById('play-pause-btn');
        const playPauseText = document.getElementById('play-pause-text');
        const playIcon = document.getElementById('play-icon');
        const skipBackBtn = document.getElementById('skip-back-btn');
        const skipForwardBtn = document.getElementById('skip-forward-btn');
        const skipAudioBtn = document.getElementById('skip-audio-btn');

        const questionPassageTitle = document.getElementById('question-passage-title');
        const questionCounter = document.getElementById('question-counter');
        const questionText = document.getElementById('question-text');
        const questionOptions = document.getElementById('question-options');

        const scoreDisplay = document.getElementById('score');
        const totalQuestionsDisplay = document.getElementById('total-questions');
        const percentageScoreDisplay = document.getElementById('percentage-score');
        const passageSummaryContainer = document.getElementById('passage-summary');
        const questionAnalysisContainer = document.getElementById('question-analysis');
        const overallAnalysisContainer = document.getElementById('overall-analysis');
        const printReportBtn = document.getElementById('print-report-btn');
        const audioElement = document.getElementById('audio-element');


        // --- FUNCTIONS ---
        function startTimer() {
            timerInterval = setInterval(() => {
                totalSeconds--;
                const minutes = Math.floor(totalSeconds / 60);
                const seconds = totalSeconds % 60;
                timerDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                if (totalSeconds <= 0) {
                    clearInterval(timerInterval);
                    showResults();
                }
            }, 1000);
        }

        function playRealAudio() {
            const passage = passages[currentPassageIndex];
            audioElement.src = passage.audioFile;
            audioElement.play().catch(e => console.error("Audio play failed:", e));
        }

        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.floor(seconds % 60);
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        function updateProgressBar() {
            if (!audioElement.duration) return;
            const percentage = (audioElement.currentTime / audioElement.duration) * 100;
            progressBar.style.width = `${percentage}%`;
            currentTimeDisplay.textContent = formatTime(audioElement.currentTime);
        }

        function updateAudioDuration() {
            if (audioElement.duration) {
                totalDurationDisplay.textContent = formatTime(audioElement.duration);
            }
        }

        function seekToPosition(percentage) {
            if (audioElement.duration) {
                const newTime = (percentage / 100) * audioElement.duration;
                audioElement.currentTime = newTime;
                updateProgressBar();
            }
        }

        function togglePlayPause() {
            if (audioElement.paused) {
                audioElement.play();
                playPauseText.textContent = 'Pause';
                playIcon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z" />`;
            } else {
                audioElement.pause();
                playPauseText.textContent = 'Play';
                playIcon.innerHTML = `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h8a2 2 0 012 2v8a2 2 0 01-2 2H8a2 2 0 01-2-2V6a2 2 0 012-2z" />`;
            }
        }

        function skipBackward() {
            audioElement.currentTime = Math.max(0, audioElement.currentTime - 10);
            updateProgressBar();
        }

        function skipForward() {
            if (audioElement.duration) {
                audioElement.currentTime = Math.min(audioElement.duration, audioElement.currentTime + 10);
                updateProgressBar();
            }
        }

        function skipAudio() {
            audioElement.currentTime = audioElement.duration;
            showQuestionScreen();
        }

        function startPassage() {
            startScreen.classList.add('hidden');
            questionScreen.classList.add('hidden');
            audioPlayerScreen.classList.remove('hidden');

            const passage = passages[currentPassageIndex];
            passageTitle.textContent = `Playing: ${passage.title}`;
            passageInstruction.textContent = `Listen carefully to the ${passage.type.toLowerCase()}. You may take notes.`;
            progressBar.style.width = '0%';
            
            playRealAudio();
        }

        function showQuestionScreen() {
            audioPlayerScreen.classList.add('hidden');
            questionScreen.classList.remove('hidden');
            loadQuestion();
        }

        function loadQuestion() {
            nextBtn.disabled = true;
            const passage = passages[currentPassageIndex];
            const question = passage.questions[currentQuestionIndex];
            
            questionPassageTitle.textContent = passage.title;
            const totalQuestionsInPassage = passage.questions.length;
            questionCounter.textContent = `Question ${currentQuestionIndex + 1} of ${totalQuestionsInPassage}`;
            
            questionText.innerHTML = question.q.replace(/\n/g, '<br>');
            questionOptions.innerHTML = '';

            question.options.forEach(optionText => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'question-option border-2 border-slate-300 p-4 rounded-lg cursor-pointer';
                optionDiv.textContent = optionText;
                optionDiv.addEventListener('click', () => selectOption(optionDiv, question.type === 'multiple'));
                questionOptions.appendChild(optionDiv);
            });
        }

        function selectOption(selectedDiv, isMultiple) {
            if (isMultiple) {
                selectedDiv.classList.toggle('selected');
            } else {
                // Deselect any previously selected option
                const allOptions = questionOptions.querySelectorAll('.question-option');
                allOptions.forEach(opt => opt.classList.remove('selected'));
                // Select the new one
                selectedDiv.classList.add('selected');
            }
            
            // Enable next button if at least one option is selected
            const selectedCount = questionOptions.querySelectorAll('.selected').length;
            nextBtn.disabled = selectedCount === 0;
        }

        function handleNextClick() {
            // Record answer
            const selectedDivs = questionOptions.querySelectorAll('.selected');
            const answers = Array.from(selectedDivs).map(div => div.textContent);
            userAnswers.push(answers);

            // Move to next question or passage
            currentQuestionIndex++;
            if (currentQuestionIndex < passages[currentPassageIndex].questions.length) {
                loadQuestion();
            } else {
                currentPassageIndex++;
                currentQuestionIndex = 0;
                if (currentPassageIndex < passages.length) {
                    startPassage();
                } else {
                    showResults();
                }
            }
        }

        function showResults() {
            clearInterval(timerInterval);
            audioElement.pause();
            audioElement.currentTime = 0;
            audioPlayerScreen.classList.add('hidden');
            questionScreen.classList.add('hidden');
            resultsScreen.classList.remove('hidden');

            generateTestReport();
        }

        function generateTestReport() {
            let correctCount = 0;
            let totalQuestionCount = 0;
            let answerIndex = 0;
            let passageResults = [];

            // Analyze each passage
            passages.forEach((passage, passageIndex) => {
                let passageCorrect = 0;
                let passageTotal = passage.questions.length;
                let questionDetails = [];

                passage.questions.forEach((question, questionIndex) => {
                    totalQuestionCount++;
                    const userAnswer = userAnswers[answerIndex] || [];
                    const correctAnswer = Array.isArray(question.answer) ? question.answer : [question.answer];

                    // For multiple choice, all correct answers must be selected and no incorrect ones.
                    const isCorrect = userAnswer.length === correctAnswer.length && userAnswer.every(ans => correctAnswer.includes(ans));

                    if (isCorrect) {
                        correctCount++;
                        passageCorrect++;
                    }

                    questionDetails.push({
                        questionNumber: questionIndex + 1,
                        question: question.q,
                        userAnswer: userAnswer,
                        correctAnswer: correctAnswer,
                        isCorrect: isCorrect,
                        isMultiple: question.type === 'multiple'
                    });

                    answerIndex++;
                });

                passageResults.push({
                    title: passage.title,
                    type: passage.type,
                    correct: passageCorrect,
                    total: passageTotal,
                    percentage: Math.round((passageCorrect / passageTotal) * 100),
                    questions: questionDetails
                });
            });

            // Update overall score
            const overallPercentage = Math.round((correctCount / totalQuestionCount) * 100);
            scoreDisplay.textContent = correctCount;
            totalQuestionsDisplay.textContent = totalQuestionCount;
            percentageScoreDisplay.textContent = overallPercentage;

            // Generate passage summaries
            generatePassageSummaries(passageResults);

            // Generate detailed question analysis
            generateQuestionAnalysis(passageResults);

            // Generate overall analysis
            generateOverallAnalysis(passageResults, correctCount, totalQuestionCount, overallPercentage);
        }
        
        function generatePassageSummaries(passageResults) {
            passageSummaryContainer.innerHTML = '';

            passageResults.forEach((passage, index) => {
                const summaryDiv = document.createElement('div');
                summaryDiv.className = 'bg-slate-50 rounded-lg p-4';

                const statusColor = passage.percentage >= 70 ? 'text-green-600' : passage.percentage >= 50 ? 'text-yellow-600' : 'text-red-600';
                const statusBg = passage.percentage >= 70 ? 'bg-green-100' : passage.percentage >= 50 ? 'bg-yellow-100' : 'bg-red-100';

                summaryDiv.innerHTML = `
                    <h4 class="font-semibold text-slate-800 mb-2">${passage.title}</h4>
                    <p class="text-sm text-slate-600 mb-2">${passage.type}</p>
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-bold ${statusColor}">${passage.correct}/${passage.total}</span>
                        <span class="px-2 py-1 rounded-full text-sm font-medium ${statusColor} ${statusBg}">${passage.percentage}%</span>
                    </div>
                `;

                passageSummaryContainer.appendChild(summaryDiv);
            });
        }

        function generateQuestionAnalysis(passageResults) {
            questionAnalysisContainer.innerHTML = '';

            passageResults.forEach((passage, passageIndex) => {
                const passageDiv = document.createElement('div');
                passageDiv.className = 'mb-6 border-b border-slate-200 pb-4';

                passageDiv.innerHTML = `<h4 class="font-semibold text-slate-800 mb-3">${passage.title}</h4>`;

                passage.questions.forEach((question, questionIndex) => {
                    const questionDiv = document.createElement('div');
                    questionDiv.className = `mb-4 p-4 rounded-lg border-l-4 ${question.isCorrect ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`;

                    const statusIcon = question.isCorrect ?
                        '<svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>' :
                        '<svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';

                    questionDiv.innerHTML = `
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0 mt-1">${statusIcon}</div>
                            <div class="flex-grow">
                                <p class="font-medium text-slate-800 mb-2">Question ${question.questionNumber}: ${question.question}</p>
                                <div class="text-sm">
                                    <p class="mb-1"><span class="font-medium">Your answer:</span> ${question.userAnswer.length > 0 ? question.userAnswer.join(', ') : 'No answer selected'}</p>
                                    <p><span class="font-medium">Correct answer:</span> ${question.correctAnswer.join(', ')}</p>
                                    ${question.isMultiple ? '<p class="text-slate-600 mt-1"><em>Multiple choice question</em></p>' : ''}
                                </div>
                            </div>
                        </div>
                    `;

                    passageDiv.appendChild(questionDiv);
                });

                questionAnalysisContainer.appendChild(passageDiv);
            });
        }

        function generateOverallAnalysis(passageResults, correctCount, totalQuestionCount, overallPercentage) {
            const conversationResults = passageResults.filter(p => p.type === 'Conversation');
            const lectureResults = passageResults.filter(p => p.type === 'Lecture');

            const conversationCorrect = conversationResults.reduce((sum, p) => sum + p.correct, 0);
            const conversationTotal = conversationResults.reduce((sum, p) => sum + p.total, 0);
            const lectureCorrect = lectureResults.reduce((sum, p) => sum + p.correct, 0);
            const lectureTotal = lectureResults.reduce((sum, p) => sum + p.total, 0);

            const conversationPercentage = conversationTotal > 0 ? Math.round((conversationCorrect / conversationTotal) * 100) : 0;
            const lecturePercentage = lectureTotal > 0 ? Math.round((lectureCorrect / lectureTotal) * 100) : 0;

            let performanceLevel = '';
            let recommendations = '';

            if (overallPercentage >= 80) {
                performanceLevel = 'Excellent';
                recommendations = 'Outstanding performance! You demonstrate strong listening comprehension skills. Continue practicing with authentic materials to maintain this level.';
            } else if (overallPercentage >= 65) {
                performanceLevel = 'Good';
                recommendations = 'Good performance overall. Focus on areas where you missed questions and practice note-taking strategies during lectures.';
            } else if (overallPercentage >= 50) {
                performanceLevel = 'Fair';
                recommendations = 'Fair performance. Consider focusing on improving your listening skills through regular practice with academic content and conversations.';
            } else {
                performanceLevel = 'Needs Improvement';
                recommendations = 'Additional practice is recommended. Focus on basic listening comprehension, vocabulary building, and note-taking strategies.';
            }

            overallAnalysisContainer.innerHTML = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div class="bg-blue-50 rounded-lg p-4">
                        <h4 class="font-semibold text-blue-800 mb-2">Conversations</h4>
                        <p class="text-2xl font-bold text-blue-600">${conversationCorrect}/${conversationTotal} (${conversationPercentage}%)</p>
                        <p class="text-sm text-blue-700 mt-1">Campus and academic conversations</p>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-4">
                        <h4 class="font-semibold text-purple-800 mb-2">Lectures</h4>
                        <p class="text-2xl font-bold text-purple-600">${lectureCorrect}/${lectureTotal} (${lecturePercentage}%)</p>
                        <p class="text-sm text-purple-700 mt-1">Academic lectures and discussions</p>
                    </div>
                </div>

                <div class="bg-slate-50 rounded-lg p-4">
                    <h4 class="font-semibold text-slate-800 mb-2">Performance Level: <span class="text-blue-600">${performanceLevel}</span></h4>
                    <p class="text-slate-700 mb-3">${recommendations}</p>

                    <div class="text-sm text-slate-600">
                        <p class="mb-1"><strong>Test Completion:</strong> ${totalQuestionCount} questions answered</p>
                        <p class="mb-1"><strong>Overall Accuracy:</strong> ${overallPercentage}%</p>
                        <p><strong>Areas for Focus:</strong> ${conversationPercentage < lecturePercentage ? 'Conversations' : lecturePercentage < conversationPercentage ? 'Lectures' : 'Continue balanced practice'}</p>
                    </div>
                </div>
            `;
        }

        function resetTest() {
            currentPassageIndex = 0;
            currentQuestionIndex = 0;
            userAnswers = [];
            totalSeconds = 36 * 60;
            timerDisplay.textContent = "36:00";

            resultsScreen.classList.add('hidden');
            startScreen.classList.remove('hidden');
        }

        // --- EVENT LISTENERS ---
        startBtn.addEventListener('click', () => {
            startTimer();
            startPassage();
        });

        nextBtn.addEventListener('click', handleNextClick);
        restartBtn.addEventListener('click', resetTest);
        printReportBtn.addEventListener('click', () => window.print());

        // Audio event listeners
        audioElement.addEventListener('timeupdate', updateProgressBar);
        audioElement.addEventListener('loadedmetadata', updateAudioDuration);
        audioElement.addEventListener('ended', showQuestionScreen);

        // Audio control event listeners
        playPauseBtn.addEventListener('click', togglePlayPause);
        skipBackBtn.addEventListener('click', skipBackward);
        skipForwardBtn.addEventListener('click', skipForward);
        skipAudioBtn.addEventListener('click', skipAudio);

        // Progress bar click and drag functionality
        let isDragging = false;

        progressContainer.addEventListener('click', (e) => {
            if (!isDragging) {
                const rect = progressContainer.getBoundingClientRect();
                const percentage = ((e.clientX - rect.left) / rect.width) * 100;
                seekToPosition(Math.max(0, Math.min(100, percentage)));
            }
        });

        progressHandle.addEventListener('mousedown', (e) => {
            isDragging = true;
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const rect = progressContainer.getBoundingClientRect();
                const percentage = ((e.clientX - rect.left) / rect.width) * 100;
                seekToPosition(Math.max(0, Math.min(100, percentage)));
            }
        });

        document.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events for mobile
        progressHandle.addEventListener('touchstart', (e) => {
            isDragging = true;
            e.preventDefault();
        });

        document.addEventListener('touchmove', (e) => {
            if (isDragging) {
                const touch = e.touches[0];
                const rect = progressContainer.getBoundingClientRect();
                const percentage = ((touch.clientX - rect.left) / rect.width) * 100;
                seekToPosition(Math.max(0, Math.min(100, percentage)));
            }
        });

        document.addEventListener('touchend', () => {
            isDragging = false;
        });

    </script>
</body>
</html>
