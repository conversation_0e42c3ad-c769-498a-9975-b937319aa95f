class TOEFLListeningTest {
    constructor() {
        this.testConfig = null;
        this.currentPassageIndex = 0;
        this.currentQuestionIndex = 0;
        this.userAnswers = {};
        this.testStartTime = null;
        this.testEndTime = null;
        this.timer = null;
        this.timeRemaining = 0;
        
        // DOM elements
        this.loadingScreen = document.getElementById('loading-screen');
        this.errorScreen = document.getElementById('error-screen');
        this.appContainer = document.getElementById('app-container');
        this.configSection = document.getElementById('config-section');
        this.startScreen = document.getElementById('start-screen');
        this.testInterface = document.getElementById('test-interface');
        this.resultsScreen = document.getElementById('results-screen');
        
        this.init();
    }

    async init() {
        try {
            await this.loadTestConfig();
            this.setupEventListeners();
            this.showConfigSelector();
        } catch (error) {
            this.showError(error.message);
        }
    }

    async loadTestConfig(configPath = 'config/test-config.json') {
        try {
            const response = await fetch(configPath);
            if (!response.ok) {
                throw new Error(`Failed to load config: ${response.status} ${response.statusText}`);
            }
            this.testConfig = await response.json();
            
            // Validate config
            if (!this.testConfig.testInfo || !this.testConfig.passages) {
                throw new Error('Invalid test configuration format');
            }
            
            console.log('Test config loaded successfully:', this.testConfig);
        } catch (error) {
            console.error('Error loading test config:', error);
            throw new Error(`Could not load test configuration: ${error.message}`);
        }
    }

    setupEventListeners() {
        // Config selector
        document.getElementById('load-config-btn').addEventListener('click', () => {
            this.loadSelectedConfig();
        });

        // Start test
        document.getElementById('start-test-btn').addEventListener('click', () => {
            this.startTest();
        });

        // Audio controls
        document.getElementById('replay-btn').addEventListener('click', () => {
            this.replayAudio();
        });

        document.getElementById('continue-btn').addEventListener('click', () => {
            this.showQuestions();
        });

        // Navigation
        document.getElementById('prev-passage-btn').addEventListener('click', () => {
            this.previousPassage();
        });

        document.getElementById('next-passage-btn').addEventListener('click', () => {
            this.nextPassage();
        });

        // Results
        document.getElementById('print-results-btn').addEventListener('click', () => {
            window.print();
        });

        document.getElementById('restart-test-btn').addEventListener('click', () => {
            this.restartTest();
        });

        // Retry button
        document.getElementById('retry-button').addEventListener('click', () => {
            this.init();
        });

        // Audio player events
        const audioPlayer = document.getElementById('audio-player');
        audioPlayer.addEventListener('ended', () => {
            document.getElementById('continue-btn').disabled = false;
        });

        audioPlayer.addEventListener('loadedmetadata', () => {
            document.getElementById('continue-btn').disabled = false;
        });
    }

    async loadSelectedConfig() {
        const configSelector = document.getElementById('config-selector');
        const selectedConfig = configSelector.value;
        
        try {
            this.loadingScreen.classList.remove('hidden');
            this.appContainer.classList.add('hidden');
            
            await this.loadTestConfig(selectedConfig);
            this.setupUI();
            this.showStartScreen();
        } catch (error) {
            this.showError(error.message);
        }
    }

    setupUI() {
        // Update test info
        document.getElementById('test-title').textContent = this.testConfig.testInfo.title;
        document.getElementById('test-logo').src = this.testConfig.testInfo.logo;
        
        // Setup timer
        this.timeRemaining = this.testConfig.testInfo.duration;
        this.updateTimerDisplay();
        
        // Setup instructions
        const instructionsContainer = document.getElementById('test-instructions');
        instructionsContainer.innerHTML = '';
        this.testConfig.testInfo.instructions.forEach(instruction => {
            const li = document.createElement('li');
            li.textContent = instruction;
            li.className = 'flex items-start space-x-2';
            li.innerHTML = `<span class="text-blue-600 font-bold">•</span><span>${instruction}</span>`;
            instructionsContainer.appendChild(li);
        });
    }

    showConfigSelector() {
        this.loadingScreen.classList.add('hidden');
        this.errorScreen.classList.add('hidden');
        this.appContainer.classList.remove('hidden');
        this.configSection.classList.remove('hidden');
        this.startScreen.classList.add('hidden');
        this.testInterface.classList.add('hidden');
        this.resultsScreen.classList.add('hidden');
    }

    showStartScreen() {
        this.loadingScreen.classList.add('hidden');
        this.errorScreen.classList.add('hidden');
        this.configSection.classList.add('hidden');
        this.startScreen.classList.remove('hidden');
        this.testInterface.classList.add('hidden');
        this.resultsScreen.classList.add('hidden');
    }

    showError(message) {
        this.loadingScreen.classList.add('hidden');
        this.appContainer.classList.add('hidden');
        this.errorScreen.classList.remove('hidden');
        document.getElementById('error-message').textContent = message;
    }

    startTest() {
        this.testStartTime = new Date();
        this.currentPassageIndex = 0;
        this.currentQuestionIndex = 0;
        this.userAnswers = {};
        
        this.startTimer();
        this.showTestInterface();
        this.loadCurrentPassage();
    }

    startTimer() {
        this.timer = setInterval(() => {
            this.timeRemaining--;
            this.updateTimerDisplay();
            
            if (this.timeRemaining <= 0) {
                this.endTest();
            } else if (this.timeRemaining <= 300) { // 5 minutes warning
                document.getElementById('timer').classList.add('timer-warning', 'text-red-600');
            }
        }, 1000);
    }

    updateTimerDisplay() {
        const minutes = Math.floor(this.timeRemaining / 60);
        const seconds = this.timeRemaining % 60;
        const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        document.getElementById('timer').textContent = display;
    }

    showTestInterface() {
        this.startScreen.classList.add('hidden');
        this.testInterface.classList.remove('hidden');
        document.getElementById('audio-section').classList.remove('hidden');
        document.getElementById('questions-section').classList.add('hidden');
    }

    loadCurrentPassage() {
        const passage = this.testConfig.passages[this.currentPassageIndex];
        
        // Update passage info
        document.getElementById('passage-title').textContent = passage.title;
        document.getElementById('passage-instruction').textContent = passage.instruction;
        document.getElementById('passage-counter').textContent = 
            `Passage ${this.currentPassageIndex + 1} of ${this.testConfig.passages.length}`;
        
        // Load audio
        const audioPlayer = document.getElementById('audio-player');
        audioPlayer.src = passage.audioFile;
        audioPlayer.load();
        
        // Reset continue button
        document.getElementById('continue-btn').disabled = true;
        
        // Update navigation buttons
        document.getElementById('prev-passage-btn').disabled = this.currentPassageIndex === 0;
        document.getElementById('next-passage-btn').textContent = 
            this.currentPassageIndex === this.testConfig.passages.length - 1 ? 'Finish Test' : 'Next Passage →';
    }

    replayAudio() {
        const audioPlayer = document.getElementById('audio-player');
        audioPlayer.currentTime = 0;
        audioPlayer.play();
    }

    showQuestions() {
        document.getElementById('audio-section').classList.add('hidden');
        document.getElementById('questions-section').classList.remove('hidden');
        this.loadCurrentQuestions();
    }

    loadCurrentQuestions() {
        const passage = this.testConfig.passages[this.currentPassageIndex];
        const questionsContainer = document.getElementById('questions-container');
        questionsContainer.innerHTML = '';
        
        passage.questions.forEach((question, index) => {
            const questionDiv = this.createQuestionElement(question, index);
            questionsContainer.appendChild(questionDiv);
        });
        
        this.updateQuestionProgress();
    }

    createQuestionElement(question, questionIndex) {
        const questionDiv = document.createElement('div');
        questionDiv.className = 'question-card bg-white rounded-lg shadow-md p-6 mb-6';
        questionDiv.id = `question-${this.currentPassageIndex}-${questionIndex}`;
        
        const questionId = `${this.currentPassageIndex}-${questionIndex}`;
        const isMultiple = question.type === 'multiple';
        
        let html = `
            <div class="mb-4">
                <h4 class="text-lg font-semibold mb-3">
                    Question ${this.getGlobalQuestionNumber(questionIndex)}
                </h4>
                <p class="text-gray-800 mb-4">${question.text}</p>
                ${isMultiple ? '<p class="text-sm text-blue-600 font-medium mb-3">Select all that apply:</p>' : ''}
            </div>
            <div class="space-y-2">
        `;
        
        question.options.forEach((option, optionIndex) => {
            const inputType = isMultiple ? 'checkbox' : 'radio';
            const inputName = isMultiple ? `question-${questionId}-${optionIndex}` : `question-${questionId}`;
            const optionId = `${questionId}-${optionIndex}`;
            
            html += `
                <label class="option-button flex items-center p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input type="${inputType}" name="${inputName}" value="${option}" 
                           id="${optionId}" class="mr-3" 
                           onchange="app.handleAnswerChange('${questionId}', '${option}', ${isMultiple})">
                    <span class="text-gray-800">${option}</span>
                </label>
            `;
        });
        
        html += '</div>';
        questionDiv.innerHTML = html;
        
        return questionDiv;
    }

    getGlobalQuestionNumber(localIndex) {
        let globalIndex = localIndex + 1;
        for (let i = 0; i < this.currentPassageIndex; i++) {
            globalIndex += this.testConfig.passages[i].questions.length;
        }
        return globalIndex;
    }

    handleAnswerChange(questionId, selectedOption, isMultiple) {
        if (!this.userAnswers[questionId]) {
            this.userAnswers[questionId] = [];
        }
        
        if (isMultiple) {
            const index = this.userAnswers[questionId].indexOf(selectedOption);
            if (index > -1) {
                this.userAnswers[questionId].splice(index, 1);
            } else {
                this.userAnswers[questionId].push(selectedOption);
            }
        } else {
            this.userAnswers[questionId] = [selectedOption];
        }
        
        this.updateQuestionAppearance(questionId);
        this.updateQuestionProgress();
    }

    updateQuestionAppearance(questionId) {
        const questionElement = document.getElementById(`question-${questionId}`);
        if (this.userAnswers[questionId] && this.userAnswers[questionId].length > 0) {
            questionElement.classList.add('answered');
        } else {
            questionElement.classList.remove('answered');
        }
    }

    updateQuestionProgress() {
        const passage = this.testConfig.passages[this.currentPassageIndex];
        const totalQuestions = passage.questions.length;
        let answeredQuestions = 0;
        
        passage.questions.forEach((_, index) => {
            const questionId = `${this.currentPassageIndex}-${index}`;
            if (this.userAnswers[questionId] && this.userAnswers[questionId].length > 0) {
                answeredQuestions++;
            }
        });
        
        const progress = (answeredQuestions / totalQuestions) * 100;
        document.getElementById('progress-bar').style.width = `${progress}%`;
        document.getElementById('question-counter').textContent = 
            `${answeredQuestions} of ${totalQuestions} questions answered`;
    }

    previousPassage() {
        if (this.currentPassageIndex > 0) {
            this.currentPassageIndex--;
            this.loadCurrentPassage();
        }
    }

    nextPassage() {
        if (this.currentPassageIndex < this.testConfig.passages.length - 1) {
            this.currentPassageIndex++;
            this.loadCurrentPassage();
        } else {
            this.endTest();
        }
    }

    endTest() {
        this.testEndTime = new Date();
        if (this.timer) {
            clearInterval(this.timer);
        }
        this.calculateResults();
        this.showResults();
    }

    calculateResults() {
        let totalQuestions = 0;
        let correctAnswers = 0;
        const passageResults = [];
        
        this.testConfig.passages.forEach((passage, passageIndex) => {
            let passageCorrect = 0;
            const questionResults = [];
            
            passage.questions.forEach((question, questionIndex) => {
                const questionId = `${passageIndex}-${questionIndex}`;
                const userAnswer = this.userAnswers[questionId] || [];
                const correctAnswer = question.correctAnswer;
                
                const isCorrect = this.arraysEqual(
                    userAnswer.sort(), 
                    correctAnswer.sort()
                );
                
                if (isCorrect) {
                    correctAnswers++;
                    passageCorrect++;
                }
                
                questionResults.push({
                    question: question.text,
                    userAnswer,
                    correctAnswer,
                    isCorrect,
                    explanation: question.explanation || ''
                });
                
                totalQuestions++;
            });
            
            passageResults.push({
                title: passage.title,
                correct: passageCorrect,
                total: passage.questions.length,
                questions: questionResults
            });
        });
        
        const percentage = Math.round((correctAnswers / totalQuestions) * 100);
        const testDuration = this.testEndTime - this.testStartTime;
        
        this.results = {
            totalQuestions,
            correctAnswers,
            percentage,
            testDuration,
            passageResults,
            performanceLevel: this.getPerformanceLevel(percentage)
        };
    }

    arraysEqual(a, b) {
        if (a.length !== b.length) return false;
        for (let i = 0; i < a.length; i++) {
            if (a[i] !== b[i]) return false;
        }
        return true;
    }

    getPerformanceLevel(percentage) {
        const levels = this.testConfig.scoring.performanceLevels;
        
        for (const [level, criteria] of Object.entries(levels)) {
            if (percentage >= criteria.min) {
                return { level, ...criteria };
            }
        }
        
        return levels.poor || { level: 'poor', message: 'Needs improvement' };
    }

    showResults() {
        this.testInterface.classList.add('hidden');
        this.resultsScreen.classList.remove('hidden');
        this.renderResults();
    }

    renderResults() {
        // Results summary
        const summaryContainer = document.getElementById('results-summary');
        const duration = Math.round(this.results.testDuration / 1000 / 60);
        
        summaryContainer.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-blue-50 p-6 rounded-lg text-center">
                    <div class="text-3xl font-bold text-blue-600">${this.results.correctAnswers}</div>
                    <div class="text-sm text-gray-600">Correct Answers</div>
                </div>
                <div class="bg-green-50 p-6 rounded-lg text-center">
                    <div class="text-3xl font-bold text-green-600">${this.results.percentage}%</div>
                    <div class="text-sm text-gray-600">Overall Score</div>
                </div>
                <div class="bg-purple-50 p-6 rounded-lg text-center">
                    <div class="text-3xl font-bold text-purple-600">${duration}</div>
                    <div class="text-sm text-gray-600">Minutes Used</div>
                </div>
                <div class="bg-yellow-50 p-6 rounded-lg text-center">
                    <div class="text-lg font-bold text-yellow-600">${this.results.performanceLevel.level.toUpperCase()}</div>
                    <div class="text-sm text-gray-600">Performance Level</div>
                </div>
            </div>
            
            <div class="bg-gray-50 p-6 rounded-lg mb-6">
                <h3 class="text-lg font-semibold mb-2">Performance Analysis</h3>
                <p class="text-gray-700">${this.results.performanceLevel.message}</p>
            </div>
        `;
        
        // Detailed results
        const detailedContainer = document.getElementById('detailed-results');
        let detailedHTML = '<h3 class="text-lg font-semibold mb-4">Detailed Results by Passage</h3>';
        
        this.results.passageResults.forEach((passage, index) => {
            const passagePercentage = Math.round((passage.correct / passage.total) * 100);
            
            detailedHTML += `
                <div class="bg-white border rounded-lg p-6 mb-4">
                    <div class="flex justify-between items-center mb-4">
                        <h4 class="text-md font-semibold">${passage.title}</h4>
                        <span class="text-sm font-medium px-3 py-1 rounded-full ${
                            passagePercentage >= 70 ? 'bg-green-100 text-green-800' : 
                            passagePercentage >= 50 ? 'bg-yellow-100 text-yellow-800' : 
                            'bg-red-100 text-red-800'
                        }">
                            ${passage.correct}/${passage.total} (${passagePercentage}%)
                        </span>
                    </div>
                    
                    <div class="space-y-3">
            `;
            
            passage.questions.forEach((question, qIndex) => {
                detailedHTML += `
                    <div class="border-l-4 ${question.isCorrect ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'} p-3">
                        <div class="text-sm font-medium mb-1">
                            Question ${this.getGlobalQuestionNumber(qIndex)} 
                            ${question.isCorrect ? '✓' : '✗'}
                        </div>
                        <div class="text-sm text-gray-600 mb-2">${question.question}</div>
                        <div class="text-xs">
                            <div><strong>Your answer:</strong> ${question.userAnswer.join(', ') || 'No answer'}</div>
                            <div><strong>Correct answer:</strong> ${question.correctAnswer.join(', ')}</div>
                            ${question.explanation ? `<div class="mt-1 text-gray-500"><strong>Explanation:</strong> ${question.explanation}</div>` : ''}
                        </div>
                    </div>
                `;
            });
            
            detailedHTML += '</div></div>';
        });
        
        detailedContainer.innerHTML = detailedHTML;
    }

    restartTest() {
        this.currentPassageIndex = 0;
        this.currentQuestionIndex = 0;
        this.userAnswers = {};
        this.testStartTime = null;
        this.testEndTime = null;
        this.results = null;
        
        if (this.timer) {
            clearInterval(this.timer);
        }
        
        this.showConfigSelector();
    }
}

// Initialize the app when DOM is loaded
let app;
document.addEventListener('DOMContentLoaded', () => {
    app = new TOEFLListeningTest();
});
