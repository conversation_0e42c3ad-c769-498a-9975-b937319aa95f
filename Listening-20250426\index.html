<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TOEFL iBT Listening Test - April 26, 2025</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @media print {
            .no-print { display: none !important; }
            .print-only { display: block !important; }
            body { font-size: 12px; }
            .page-break { page-break-before: always; }
        }
        .print-only { display: none; }
        
        .audio-controls {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .progress-bar {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            overflow: hidden;
        }
        
        .progress-fill {
            background: #4ade80;
            height: 100%;
            transition: width 0.1s ease;
        }
        
        .question-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        
        .question-card.answered {
            border-left-color: #10b981;
            background-color: #f0fdf4;
        }
        
        .option-button {
            transition: all 0.2s ease;
        }
        
        .option-button:hover {
            transform: translateX(4px);
        }
        
        .option-button.selected {
            background-color: #3b82f6;
            color: white;
            transform: translateX(4px);
        }
        
        .timer-warning {
            animation: pulse 1s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-white flex items-center justify-center z-50">
        <div class="text-center">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 class="text-xl font-semibold text-gray-700">Loading TOEFL Test...</h2>
            <p class="text-gray-500 mt-2">Please wait while we prepare your test</p>
        </div>
    </div>

    <!-- Error Screen -->
    <div id="error-screen" class="fixed inset-0 bg-white flex items-center justify-center z-40 hidden">
        <div class="text-center max-w-md mx-auto p-6">
            <div class="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 class="text-2xl font-bold text-red-600 mb-4">Error Loading Test</h2>
            <p id="error-message" class="text-gray-600 mb-6">Could not load test configuration</p>
            <button id="retry-button" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Retry
            </button>
        </div>
    </div>

    <!-- Main App Container -->
    <div id="app-container" class="hidden">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b no-print">
            <div class="max-w-6xl mx-auto px-4 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <img id="test-logo" src="assets/icon.png" alt="TOEFL" class="h-10 w-10">
                        <div>
                            <h1 id="test-title" class="text-xl font-bold text-gray-900">TOEFL iBT Listening Test</h1>
                            <p class="text-sm text-gray-600">April 26, 2025 Edition</p>
                        </div>
                    </div>
                    <div id="timer-display" class="text-right">
                        <div class="text-2xl font-mono font-bold text-blue-600" id="timer">00:00</div>
                        <div class="text-sm text-gray-500">Time Remaining</div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Configuration Selector -->
        <div id="config-section" class="max-w-4xl mx-auto px-4 py-6 no-print">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold mb-4">Select Test Configuration</h2>
                <select id="config-selector" class="w-full p-3 border border-gray-300 rounded-md bg-white mb-4">
                    <option value="config/test-config.json">Full TOEFL Test - April 26, 2025 (7 passages, 34 questions)</option>
                </select>
                <button id="load-config-btn" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Load Selected Test
                </button>
            </div>
        </div>

        <!-- Start Screen -->
        <div id="start-screen" class="max-w-4xl mx-auto px-4 py-8 hidden">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6 text-center">TOEFL iBT Listening Section</h2>
                
                <div class="mb-6">
                    <h3 class="text-lg font-semibold mb-3">Test Instructions:</h3>
                    <div id="test-instructions" class="space-y-2 text-gray-700">
                        <!-- Instructions will be loaded from JSON -->
                    </div>
                </div>

                <div class="text-center">
                    <button id="start-test-btn" class="bg-green-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-green-700 transition-colors">
                        Start Test
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Interface -->
        <div id="test-interface" class="hidden">
            <!-- Audio Player -->
            <div id="audio-section" class="audio-controls text-white p-6 no-print">
                <div class="max-w-4xl mx-auto">
                    <div class="flex items-center justify-between mb-4">
                        <h3 id="passage-title" class="text-xl font-semibold">Passage Title</h3>
                        <div class="text-sm opacity-90">
                            <span id="passage-counter">Passage 1 of 7</span>
                        </div>
                    </div>
                    
                    <div class="bg-white bg-opacity-20 rounded-lg p-4">
                        <div id="passage-instruction" class="text-sm mb-4 opacity-90">
                            Listen carefully to the audio passage.
                        </div>
                        
                        <audio id="audio-player" class="w-full mb-4" controls>
                            Your browser does not support the audio element.
                        </audio>
                        
                        <div class="flex items-center justify-between">
                            <button id="replay-btn" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded transition-colors">
                                🔄 Replay
                            </button>
                            <button id="continue-btn" class="bg-green-500 hover:bg-green-600 px-6 py-2 rounded font-semibold transition-colors" disabled>
                                Continue to Questions
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Questions Section -->
            <div id="questions-section" class="max-w-4xl mx-auto px-4 py-6 hidden">
                <div class="mb-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">Questions</h3>
                        <div class="text-sm text-gray-600">
                            <span id="question-counter">Question 1 of 6</span>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>

                <div id="questions-container">
                    <!-- Questions will be dynamically loaded -->
                </div>

                <div class="flex justify-between mt-8 no-print">
                    <button id="prev-passage-btn" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors" disabled>
                        ← Previous Passage
                    </button>
                    <button id="next-passage-btn" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Next Passage →
                    </button>
                </div>
            </div>
        </div>

        <!-- Results Screen -->
        <div id="results-screen" class="max-w-4xl mx-auto px-4 py-8 hidden">
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-6 text-center">Test Results</h2>
                
                <div id="results-summary" class="mb-8">
                    <!-- Results summary will be populated by JavaScript -->
                </div>

                <div id="detailed-results" class="mb-8">
                    <!-- Detailed results will be populated by JavaScript -->
                </div>

                <div class="text-center no-print">
                    <button id="print-results-btn" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors mr-4">
                        Print Results
                    </button>
                    <button id="restart-test-btn" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                        Take Another Test
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="js/app.js"></script>
</body>
</html>
