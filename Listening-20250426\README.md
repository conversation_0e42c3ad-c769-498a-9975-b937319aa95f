# TOEFL iBT Listening Test - April 26, 2025 Edition

This is a complete TOEFL iBT Listening test based on authentic test materials from April 26, 2025. The test includes 1 conversation and 6 lectures with a total of 41 questions.

## Test Structure

### Conversation (C1)
- **Topic**: Student and Professor Discussion
- **Questions**: 5
- **Duration**: ~3 minutes

### Lectures
1. **L1 - Dance History**: Labanotation (6 questions)
2. **L2 - Psychology**: Decision Making and Self-Control (6 questions)  
3. **L3 - History**: The Impact of Potatoes (6 questions)
4. **L4 - Environmental Policy**: Nuclear Fusion (6 questions)
5. **L5 - Biology**: Butterfly Defense Mechanisms (6 questions)
6. **L6 - Child Development**: Executive Function (6 questions)

## Features

- **Authentic Audio**: Original TOEFL test audio files
- **Interactive Interface**: Modern web-based test interface
- **Comprehensive Scoring**: Detailed performance analysis
- **Mobile Friendly**: Responsive design works on all devices
- **Print Support**: Print-friendly results page

## How to Use

### Option 1: Local Server (Recommended)
Due to browser security restrictions, you need to run a local server:

1. **Using Python** (if installed):
   ```bash
   cd Listening-20250426
   python -m http.server 8000
   ```
   Then open: http://localhost:8000

2. **Using Node.js** (if installed):
   ```bash
   cd Listening-20250426
   npx http-server -p 8000
   ```
   Then open: http://localhost:8000

3. **Using PHP** (if installed):
   ```bash
   cd Listening-20250426
   php -S localhost:8000
   ```
   Then open: http://localhost:8000

### Option 2: Direct File Access
If you encounter CORS errors when opening `index.html` directly in your browser, you must use one of the server methods above.

## Test Instructions

1. **Listen Carefully**: Each audio passage can be replayed once
2. **Answer All Questions**: Some questions may have multiple correct answers
3. **Time Management**: You have approximately 60 minutes total
4. **Navigation**: Use Previous/Next buttons to move between passages
5. **Review**: Check your answers before finishing each passage

## Scoring

- **Total Questions**: 41
- **Passing Score**: 70%
- **Performance Levels**:
  - Excellent: 90%+ 
  - Good: 80-89%
  - Satisfactory: 70-79%
  - Needs Improvement: 60-69%
  - Poor: Below 60%

## Technical Requirements

- Modern web browser (Chrome, Firefox, Safari, Edge)
- Audio playback capability
- JavaScript enabled
- Internet connection (for Tailwind CSS)

## File Structure

```
Listening-20250426/
├── index.html              # Main test interface
├── js/
│   └── app.js              # Test application logic
├── config/
│   └── test-config.json    # Test questions and configuration
├── audio/                  # Audio files
│   ├── C1.mp3             # Conversation 1
│   ├── L1.mp3             # Lecture 1 (Dance History)
│   ├── L2.mp3             # Lecture 2 (Psychology)
│   ├── L3.mp3             # Lecture 3 (History)
│   ├── L4.mp3             # Lecture 4 (Environmental Policy)
│   ├── L5.mp3             # Lecture 5 (Biology)
│   └── L6.mp3             # Lecture 6 (Child Development)
├── assets/
│   └── icon.png           # TOEFL logo
└── README.md              # This file
```

## Answer Keys

The correct answers are embedded in the test configuration and will be revealed in the results screen after completing the test.

## Notes

- This test is based on authentic TOEFL materials from April 26, 2025
- Audio transcripts are available in the source materials
- All questions follow official TOEFL iBT format and difficulty
- The test interface mimics the actual TOEFL iBT experience

## Troubleshooting

**Audio not playing?**
- Check your browser's audio settings
- Ensure audio files are in the correct directory
- Try refreshing the page

**CORS errors?**
- Use a local server instead of opening the HTML file directly
- See "How to Use" section above for server setup instructions

**Questions not loading?**
- Check that `config/test-config.json` exists and is valid JSON
- Ensure you're running from a server (not file://)

## Support

This test was created using the JSON-driven TOEFL test system. For technical issues or questions about the test format, refer to the main project documentation.
